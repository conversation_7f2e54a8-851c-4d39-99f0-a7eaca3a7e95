/* global stuff */


@import url("https://use.typekit.net/thk7oyq.css");

.eee-dashboard-card *  {
font-family: "Gotham", Arial, sans-serif !important;
transition: all 0.15s ease-out;
letter-spacing: 0px !important;
}

#search-list-config th div, #search-list-config td div {
width: 100%;
}

/* Ensure consistent styling for list items */
#search-list-config tr {
  background-color: #ffffff;
}

#search-list-config tr:nth-child(4n+3),
#search-list-config tr:nth-child(4n+4) {
  background-color: #f9f9f9;
}



/* Remove border between the two rows of each list item */
#search-list-config tr:nth-child(4n+3) {
  border-bottom: none;
}

#search-list-config tr:nth-child(4n+1) {
  border-bottom: none;
}

#search-list-config td {
  vertical-align: top;
}

/* Adjust table layout for better column sizing */
#search-list-config {
  table-layout: fixed;
  width: 100%;
}

#search-list-config th,
#search-list-config td {
  width: 20%;
}

/* Add top margin to icons in first and last columns */
#search-list-config td:nth-child(1) .drag-handle,
#search-list-config td:nth-child(6) button {
  margin-top: 16px;
}

button.add-button {
  margin-top: 20px;
}

#search-list-config tr {
    display: block;
    width: 100%;
    height: auto;
    border-top: 2px solid #000 !important;
    padding: 16px 0px;
    position: relative;
}

#search-list-configtr td.MuiTableCell-root, #search-list-config tr th {
display: inline-block;
border: 0px !important;
}

#search-list-config tr th {
    width: calc(100% - 50px);
}

#search-list-configtr td label span {
    text-align: left !important;
}

.eds-ltr-15io2nm {
border: 0px !important;
padding: 0px 16px;
}

.ActionMenu {
    position: absolute;
    top: 10%;
    right: -7%;
}