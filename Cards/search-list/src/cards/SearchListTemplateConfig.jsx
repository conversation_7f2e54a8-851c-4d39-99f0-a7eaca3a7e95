import React from "react";
import PropTypes from "prop-types";
import {
  TextField,
  IconButton,
  Table,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Button,
  ActionMenu,
  ActionMenuItem
} from "@ellucian/react-design-system/core";
import { Icon } from "@ellucian/ds-icons/lib";
import "./styles.css";

const SearchListTemplateConfig = (props) => {
  const {
    cardControl: { setCustomConfiguration, setIsCustomConfigurationValid },
    cardInfo: {
      configuration: { customConfiguration }
    }
  } = props;

  const client = customConfiguration ? customConfiguration.client : undefined;
  const [items, setItems] = React.useState(
    client ? client.items : [{ title: "", description: "", url: "", icon: "", keywords: "", audience: "", category: "", links: [] }]
  );
  const [iconColor, setIconColor] = React.useState(
    client ? client.iconColor : "#056CF9"
  );



  const colorOptions = [
    { label: "White", value: "#ffffff" },
    { label: "Black", value: "#000000" },
    { label: "Red", value: "#F81423" },
    { label: "Orange", value: "#F26323" },
    { label: "Yellow", value: "#F6FE4F" },
    { label: "Green", value: "#3CAB49" },
    { label: "Light Blue", value: "#1AB0F6" },
    { label: "Blue", value: "#056CF9" },
    { label: "Purple", value: "#A014EC" }
  ];

  const handleChange = (i, e) => {
    const newItems = [...items];
    newItems[i][e.target.name] = e.target.value;
    setItems(newItems);

    setCustomConfiguration({
      customConfiguration: {
        client: {
          items: newItems,
          iconColor: iconColor
        }
      }
    });
  };

  const handleColorChange = (e) => {
    const newColor = e.target.value;
    setIconColor(newColor);

    setCustomConfiguration({
      customConfiguration: {
        client: {
          items: items,
          iconColor: newColor
        }
      }
    });
  };

  const addItem = () => {
    setItems([...items, { title: "", description: "", url: "", icon: "", keywords: "", audience: "", category: "", links: [] }]);
  };

  const removeItem = (i) => {
    // Prevent deletion if there's only one item left
    if (items.length <= 1) {
      return;
    }

    const newItems = [...items];
    newItems.splice(i, 1);
    setItems(newItems);

    setCustomConfiguration({
      customConfiguration: {
        client: {
          items: newItems,
          iconColor: iconColor
        }
      }
    });
  };

  const handleBlur = (e) => {
    setIsCustomConfigurationValid(e.target.value !== "");
  };

  const moveItemUp = (index) => {
    if (index === 0) return; // Can't move first item up

    const newItems = [...items];
    const temp = newItems[index];
    newItems[index] = newItems[index - 1];
    newItems[index - 1] = temp;

    setItems(newItems);
    updateConfiguration(newItems);
  };

  const moveItemDown = (index) => {
    if (index === items.length - 1) return; // Can't move last item down

    const newItems = [...items];
    const temp = newItems[index];
    newItems[index] = newItems[index + 1];
    newItems[index + 1] = temp;

    setItems(newItems);
    updateConfiguration(newItems);
  };

  const moveItemToTop = (index) => {
    if (index === 0) return; // Already at top

    const newItems = [...items];
    const item = newItems.splice(index, 1)[0];
    newItems.unshift(item);

    setItems(newItems);
    updateConfiguration(newItems);
  };

  const updateConfiguration = (newItems) => {
    setCustomConfiguration({
      customConfiguration: {
        client: {
          items: newItems,
          iconColor: iconColor
        }
      }
    });
  };

  // Functions for managing links
  const addLink = (itemIndex) => {
    const newItems = [...items];
    if (!newItems[itemIndex].links) {
      newItems[itemIndex].links = [];
    }
    newItems[itemIndex].links.push({ label: "", url: "" });
    setItems(newItems);
    updateConfiguration(newItems);
  };

  const removeLink = (itemIndex, linkIndex) => {
    const newItems = [...items];
    newItems[itemIndex].links.splice(linkIndex, 1);
    setItems(newItems);
    updateConfiguration(newItems);
  };

  const handleLinkChange = (itemIndex, linkIndex, field, value) => {
    const newItems = [...items];
    if (!newItems[itemIndex].links) {
      newItems[itemIndex].links = [];
    }
    newItems[itemIndex].links[linkIndex][field] = value;
    setItems(newItems);
    updateConfiguration(newItems);
  };



  const exportData = async () => {
    // Create a formatted string with all the list items data
    const exportText = items.map((item, index) => {
      let linksText = '';
      if (item.links && item.links.length > 0) {
        linksText = '\nLinks:\n' + item.links.map((link, linkIndex) =>
          `  ${linkIndex + 1}. ${link.label || 'Untitled'}: ${link.url || ''}`
        ).join('\n');
      }

      return `Item ${index + 1}:
Title: ${item.title || ''}
Description: ${item.description || ''}
URL: ${item.url || ''}
Icon: ${item.icon || ''}
Keywords: ${item.keywords || ''}
Audience: ${item.audience || ''}
Category: ${item.category || ''}${linksText}
Icon Color: ${iconColor}
---`;
    }).join('\n\n');

    try {
      // Copy to clipboard
      await navigator.clipboard.writeText(exportText);

      // Show success alert
      alert('List items copied to clipboard');
      console.log('Data exported to clipboard successfully');
    } catch (err) {
      console.error('Failed to copy data to clipboard:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = exportText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      // Show success alert for fallback method too
      alert('List items copied to clipboard');
    }
  };

  return (
    <div>
      <Typography variant="h3">Configure list items that will be searchable</Typography>
      <Typography variant="body2" style={{ marginBottom: '16px' }}>
        To find the names of icons available, please visit the <a href="https://fontawesome.com/search?ip=classic&s=solid&o=r" target="_blank" rel="noopener noreferrer">Font Awesome Library</a>
      </Typography>

      <div style={{ marginBottom: '32px' }}>
        <Typography variant="h4" style={{ marginBottom: '16px', fontWeight: 600 }}>
          Additional Configuration
        </Typography>

        <div style={{ display: 'flex', gap: '32px', alignItems: 'flex-start', flexWrap: 'wrap' }}>
          {/* Export Data Section */}
          <div style={{ flex: '1', minWidth: '250px' }}>
            <Typography variant="body1" style={{ marginBottom: '8px', fontWeight: 500 }}>
              Export Data
            </Typography>
            <Button
              onClick={exportData}
              variant="contained"
              style={{
                backgroundColor: '#056CF9',
                color: 'white',
                marginBottom: '8px'
              }}
            >
              Export Data
            </Button>
            <Typography variant="body2" style={{ color: '#666', fontSize: '12px' }}>
              Copy all list items to clipboard
            </Typography>
          </div>

          {/* Icon Color Section */}
          <div style={{ flex: '1', minWidth: '250px' }}>
            <Typography variant="body1" style={{ marginBottom: '8px', fontWeight: 500 }}>
              Icon Color
            </Typography>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <select
                value={iconColor}
                onChange={handleColorChange}
                style={{
                  padding: '8px 12px',
                  borderRadius: '4px',
                  fontSize: '14px',
                  border: '1px solid #b2b3b7',
                  backgroundColor: 'white',
                  minWidth: '200px',
                  height: '40px'
                }}
              >
                {colorOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <div style={{
                width: '40px',
                height: '40px',
                backgroundColor: iconColor,
                border: '1px solid #ccc',
                borderRadius: '3px'
              }} />
            </div>
          </div>
        </div>
      </div>
      <Table id="search-list-config">
        <TableBody>
          {items.map((element, index) => (
            <React.Fragment key={index}>
              <TableRow>
                <TableCell component="th" scope="row">
                  <TextField
                    label="Title"
                    margin="normal"
                    name="title"
                    helperText="Title of item"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.title}
                    fullWidth
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Description"
                    margin="normal"
                    name="description"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.description}
                    inputProps={{ maxLength: 120 }}
                    helperText={`${(element.description || '').length}/120 characters`}
                    fullWidth
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="URL"
                    margin="normal"
                    name="url"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.url}
                    fullWidth
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Icon"
                    margin="normal"
                    name="icon"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.icon}
                    fullWidth
                  />
                </TableCell>
                <br></br>
                <TableCell>
                  <TextField
                    label="Keywords"
                    margin="normal"
                    name="keywords"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.keywords || ''}
                    helperText="Hidden searchable keywords"
                    fullWidth
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Audience"
                    margin="normal"
                    name="audience"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.audience || ''}
                    helperText="Comma-separated roles (leave empty for all)"
                    fullWidth
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Category"
                    margin="normal"
                    name="category"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.category || ''}
                    helperText="Group items by category"
                    fullWidth
                  />
                </TableCell>
                
                <TableCell></TableCell>

                <TableCell class="ActionMenu" style={{ textAlign: 'center', verticalAlign: 'middle' }}>
                  <ActionMenu id={`action-menu-${index}`}>
                    <ActionMenuItem
                      disabled={index === 0}
                      onClick={() => moveItemToTop(index)}
                    >
                      <Icon name="arrow-to-top" style={{ marginRight: '8px' }} />
                      Move to Top
                    </ActionMenuItem>
                    <ActionMenuItem
                      disabled={index === 0}
                      onClick={() => moveItemUp(index)}
                    >
                      <Icon name="chevron-up" style={{ marginRight: '8px' }} />
                      Move Up
                    </ActionMenuItem>
                    <ActionMenuItem
                      disabled={index === items.length - 1}
                      onClick={() => moveItemDown(index)}
                    >
                      <Icon name="chevron-down" style={{ marginRight: '8px' }} />
                      Move Down
                    </ActionMenuItem>
                    <ActionMenuItem
                      disabled={items.length <= 1}
                      onClick={() => removeItem(index)}
                    >
                      <Icon name="trash" style={{ marginRight: '8px', color: 'red' }} />
                      Delete
                    </ActionMenuItem>
                  </ActionMenu>
                </TableCell>
              </TableRow>

              {/* Links Configuration Row */}
              <TableRow>
                <TableCell colSpan={8} style={{ padding: '16px' }}>
                  <Typography variant="h6" style={{ marginBottom: '12px' }}>
                    Multiple Links (Optional)
                  </Typography>
                  <Typography variant="body2" style={{ marginBottom: '16px', color: '#666' }}>
                    Add multiple links to show as buttons. If no links are added, the URL field above will be used.
                  </Typography>

                  {element.links && element.links.length > 0 && (
                    <div style={{ marginBottom: '16px' }}>
                      {element.links.map((link, linkIndex) => (
                        <div key={linkIndex} style={{ display: 'flex', gap: '8px', marginBottom: '8px', alignItems: 'center' }}>
                          <TextField
                            label="Link Label"
                            value={link.label || ''}
                            onChange={(e) => handleLinkChange(index, linkIndex, 'label', e.target.value)}
                            size="small"
                            style={{ flex: 1 }}
                          />
                          <TextField
                            label="Link URL"
                            value={link.url || ''}
                            onChange={(e) => handleLinkChange(index, linkIndex, 'url', e.target.value)}
                            size="small"
                            style={{ flex: 2 }}
                          />
                          <IconButton
                            onClick={() => removeLink(index, linkIndex)}
                            size="small"
                            style={{ color: 'red' }}
                          >
                            <Icon name="trash" color="white" />
                          </IconButton>
                        </div>
                      ))}
                    </div>
                  )}

                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => addLink(index)}
                    startIcon={<Icon name="add"
                    backgrounColor="black />}
                  >
                    Add Link
                  </Button>
                </TableCell>
              </TableRow>
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
      <IconButton className="add-button" onClick={() => addItem()}>
        <Icon name="add" />
      </IconButton>


    </div>
  );
};

SearchListTemplateConfig.propTypes = {
  cardControl: PropTypes.object,
  cardInfo: PropTypes.object
};

export default SearchListTemplateConfig;
